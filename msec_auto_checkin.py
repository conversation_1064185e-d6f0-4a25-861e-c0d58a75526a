#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MSEC自动签到脚本
功能：模拟用户登录绿盟科技MSEC平台并执行自动签到
作者：AI Assistant
版本：1.0.0
"""

import requests
import json
import base64
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import os
import sys
import sqlite3
import hashlib
import platform
import uuid
import argparse
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


def get_week_checkin_params() -> Tuple[str, int]:
    """
    获取本周签到历史查询参数

    Returns:
        (start_date, days) 元组
        start_date: 本周周一的日期 (YYYY-MM-DD格式)
        days: 今天是本周的第N天
    """
    today = datetime.now()
    # 获取今天是星期几 (0=周一, 6=周日)
    weekday = today.weekday()

    # 计算本周周一的日期
    monday = today - timedelta(days=weekday)
    start_date = monday.strftime('%Y-%m-%d')

    # 今天是本周的第几天 (周一=1, 周二=2, ..., 周日=7)
    days = weekday + 1

    return start_date, days


class MSECCrypto:
    """加密解密工具类"""

    @staticmethod
    def get_device_fingerprint() -> str:
        """
        生成设备指纹

        Returns:
            设备指纹字符串
        """
        # 收集设备信息
        machine = platform.machine()
        processor = platform.processor()
        system = platform.system()
        node = platform.node()

        # 尝试获取MAC地址
        try:
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                           for elements in range(0, 2*6, 2)][::-1])
        except:
            mac = "unknown"

        # 组合设备信息
        device_info = f"{system}-{machine}-{processor}-{node}-{mac}"

        # 生成指纹哈希
        fingerprint = hashlib.sha256(device_info.encode()).hexdigest()[:32]
        return fingerprint

    @staticmethod
    def generate_master_key(master_password: str, device_fingerprint: str, salt: bytes = None) -> Tuple[bytes, bytes]:
        """
        基于主密码和设备指纹生成主密钥

        Args:
            master_password: 主密码
            device_fingerprint: 设备指纹
            salt: 盐值，如果为None则生成新的

        Returns:
            (key, salt) 元组
        """
        if salt is None:
            salt = os.urandom(16)

        # 组合主密码和设备指纹
        combined_secret = f"{master_password}:{device_fingerprint}"

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(combined_secret.encode()))
        return key, salt

    @staticmethod
    def generate_key_from_password(password: str, salt: bytes = None) -> Tuple[bytes, bytes]:
        """
        基于密码生成加密密钥

        Args:
            password: 用户密码
            salt: 盐值，如果为None则生成新的

        Returns:
            (key, salt) 元组
        """
        if salt is None:
            salt = os.urandom(16)

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key, salt

    @staticmethod
    def encrypt_data(data: str, password: str) -> str:
        """
        加密数据

        Args:
            data: 要加密的数据
            password: 用户密码

        Returns:
            加密后的数据（base64编码）
        """
        key, salt = MSECCrypto.generate_key_from_password(password)
        fernet = Fernet(key)
        encrypted_data = fernet.encrypt(data.encode())

        # 将salt和加密数据组合
        combined = salt + encrypted_data
        return base64.b64encode(combined).decode()

    @staticmethod
    def decrypt_data(encrypted_data: str, password: str) -> Optional[str]:
        """
        解密数据

        Args:
            encrypted_data: 加密的数据（base64编码）
            password: 用户密码

        Returns:
            解密后的数据，失败返回None
        """
        try:
            combined = base64.b64decode(encrypted_data.encode())
            salt = combined[:16]  # 前16字节是salt
            encrypted_content = combined[16:]  # 剩余部分是加密数据

            key, _ = MSECCrypto.generate_key_from_password(password, salt)
            fernet = Fernet(key)
            decrypted_data = fernet.decrypt(encrypted_content)

            return decrypted_data.decode()
        except Exception:
            return None

    @staticmethod
    def encrypt_credentials(username: str, password: str, master_password: str) -> Tuple[str, str, str]:
        """
        加密用户凭据

        Args:
            username: 用户名
            password: 密码
            master_password: 主密码

        Returns:
            (encrypted_username, encrypted_password, device_fingerprint) 元组
        """
        device_fingerprint = MSECCrypto.get_device_fingerprint()

        # 加密用户名
        key, salt = MSECCrypto.generate_master_key(master_password, device_fingerprint)
        fernet = Fernet(key)

        encrypted_username_data = fernet.encrypt(username.encode())
        encrypted_password_data = fernet.encrypt(password.encode())

        # 将salt和加密数据组合
        username_combined = salt + encrypted_username_data
        password_combined = salt + encrypted_password_data

        encrypted_username = base64.b64encode(username_combined).decode()
        encrypted_password = base64.b64encode(password_combined).decode()

        return encrypted_username, encrypted_password, device_fingerprint

    @staticmethod
    def decrypt_credentials(encrypted_username: str, encrypted_password: str,
                          master_password: str, device_fingerprint: str) -> Tuple[Optional[str], Optional[str]]:
        """
        解密用户凭据

        Args:
            encrypted_username: 加密的用户名
            encrypted_password: 加密的密码
            master_password: 主密码
            device_fingerprint: 设备指纹

        Returns:
            (username, password) 元组，失败返回 (None, None)
        """
        try:
            # 解密用户名
            username_combined = base64.b64decode(encrypted_username.encode())
            username_salt = username_combined[:16]
            username_encrypted_content = username_combined[16:]

            username_key, _ = MSECCrypto.generate_master_key(master_password, device_fingerprint, username_salt)
            username_fernet = Fernet(username_key)
            username = username_fernet.decrypt(username_encrypted_content).decode()

            # 解密密码
            password_combined = base64.b64decode(encrypted_password.encode())
            password_salt = password_combined[:16]
            password_encrypted_content = password_combined[16:]

            password_key, _ = MSECCrypto.generate_master_key(master_password, device_fingerprint, password_salt)
            password_fernet = Fernet(password_key)
            password = password_fernet.decrypt(password_encrypted_content).decode()

            return username, password

        except Exception:
            return None, None


class MSECDatabase:
    """数据库管理类"""

    def __init__(self, db_path: str = "msec_data.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            # 设置UTF-8编码
            conn.execute("PRAGMA encoding = 'UTF-8'")
            conn.text_factory = str
            cursor = conn.cursor()

            # 创建用户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    encrypted_username TEXT,
                    encrypted_password TEXT,
                    encrypted_token TEXT,
                    token_exp INTEGER,
                    roles TEXT,
                    checkin_history TEXT,
                    device_fingerprint TEXT,
                    last_login TIMESTAMP,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()

    def save_credentials(self, username: str, password: str, master_password: str) -> bool:
        """
        保存用户凭据

        Args:
            username: 用户名
            password: 密码
            master_password: 主密码

        Returns:
            保存是否成功
        """
        try:
            # 加密凭据
            encrypted_username, encrypted_password, device_fingerprint = MSECCrypto.encrypt_credentials(
                username, password, master_password
            )

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA encoding = 'UTF-8'")
                conn.text_factory = str
                cursor = conn.cursor()

                # 检查用户是否已存在
                cursor.execute('SELECT username FROM users WHERE username = ?', (username,))
                exists = cursor.fetchone()

                if exists:
                    # 更新现有用户的凭据
                    cursor.execute('''
                        UPDATE users SET
                        encrypted_username = ?, encrypted_password = ?, device_fingerprint = ?,
                        last_updated = CURRENT_TIMESTAMP
                        WHERE username = ?
                    ''', (encrypted_username, encrypted_password, device_fingerprint, username))
                else:
                    # 插入新用户（临时ID，等获取用户信息后更新）
                    temp_id = f"temp_{username}_{int(time.time())}"
                    cursor.execute('''
                        INSERT INTO users
                        (id, username, encrypted_username, encrypted_password, device_fingerprint, created_at)
                        VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (temp_id, username, encrypted_username, encrypted_password, device_fingerprint))

                conn.commit()
                print(f"用户凭据保存成功: {username}")
                return True

        except Exception as e:
            print(f"保存用户凭据失败: {e}")
            return False

    def get_credentials(self, username: str, master_password: str) -> Tuple[Optional[str], Optional[str]]:
        """
        获取用户凭据

        Args:
            username: 用户名
            master_password: 主密码

        Returns:
            (username, password) 元组或 (None, None)
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA encoding = 'UTF-8'")
                conn.text_factory = str
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT encrypted_username, encrypted_password, device_fingerprint
                    FROM users WHERE username = ?
                ''', (username,))

                result = cursor.fetchone()
                if not result:
                    return None, None

                encrypted_username, encrypted_password, device_fingerprint = result

                # 解密凭据
                decrypted_username, decrypted_password = MSECCrypto.decrypt_credentials(
                    encrypted_username, encrypted_password, master_password, device_fingerprint
                )

                return decrypted_username, decrypted_password

        except Exception as e:
            print(f"获取用户凭据失败: {e}")
            return None, None

    def list_saved_users(self) -> list:
        """
        列出所有已保存的用户

        Returns:
            用户名列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA encoding = 'UTF-8'")
                conn.text_factory = str
                cursor = conn.cursor()
                cursor.execute('SELECT username FROM users ORDER BY last_updated DESC')
                results = cursor.fetchall()
                return [row[0] for row in results]
        except Exception as e:
            print(f"获取用户列表失败: {e}")
            return []

    def save_user_data(self, user_id: str, username: str, password: str, token: str = None,
                      token_exp: int = None, roles: Dict = None,
                      checkin_history: Dict = None) -> bool:
        """
        保存用户数据

        Args:
            user_id: 用户ID（来自API响应）
            username: 用户名
            password: 用户密码（用于加密）
            token: JWT token
            token_exp: token过期时间戳
            roles: 角色信息
            checkin_history: 签到历史

        Returns:
            保存是否成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA encoding = 'UTF-8'")
                conn.text_factory = str
                cursor = conn.cursor()

                # 加密token
                encrypted_token = None
                if token:
                    try:
                        encrypted_token = MSECCrypto.encrypt_data(token, password)
                    except Exception as e:
                        return False

                # 序列化JSON数据，确保UTF-8编码
                roles_json = json.dumps(roles, ensure_ascii=False) if roles else None
                history_json = json.dumps(checkin_history, ensure_ascii=False) if checkin_history else None

                # 检查用户是否已存在（按用户名查找，因为可能有临时ID）
                cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
                existing_record = cursor.fetchone()

                if existing_record:
                    # 用户已存在，增量更新
                    update_fields = []
                    update_values = []

                    # 首先更新用户ID（如果不同）
                    existing_id = existing_record[0]
                    if existing_id != user_id:
                        update_fields.append('id = ?')
                        update_values.append(user_id)

                    if encrypted_token is not None:
                        update_fields.append('encrypted_token = ?')
                        update_values.append(encrypted_token)

                    if token_exp is not None:
                        update_fields.append('token_exp = ?')
                        update_values.append(token_exp)

                    if roles_json is not None:
                        update_fields.append('roles = ?')
                        update_values.append(roles_json)

                    if history_json is not None:
                        update_fields.append('checkin_history = ?')
                        update_values.append(history_json)

                    if update_fields:
                        update_fields.append('last_updated = CURRENT_TIMESTAMP')
                        update_values.append(username)  # 使用username作为WHERE条件

                        sql = f"UPDATE users SET {', '.join(update_fields)} WHERE username = ?"
                        cursor.execute(sql, update_values)
                else:
                    # 用户不存在，插入新记录
                    cursor.execute('''
                        INSERT INTO users
                        (id, username, encrypted_token, token_exp, roles, checkin_history, last_updated)
                        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (user_id, username, encrypted_token, token_exp, roles_json, history_json))

                conn.commit()
                print(f"用户数据保存成功: {username}")
                return True

        except Exception as e:
            print(f"保存用户数据失败: {e}")
            return False

    def get_user_data(self, username: str, password: str) -> Optional[Dict]:
        """
        获取用户数据

        Args:
            username: 用户名
            password: 用户密码（用于解密）

        Returns:
            用户数据字典或None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("PRAGMA encoding = 'UTF-8'")
                conn.text_factory = str
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT encrypted_token, token_exp, roles, checkin_history, last_updated
                    FROM users WHERE username = ?
                ''', (username,))

                result = cursor.fetchone()
                if not result:
                    print(f"未找到用户数据: {username}")
                    return None

                encrypted_token, token_exp, roles_json, history_json, last_updated = result
                # 解密token
                token = None
                if encrypted_token:
                    try:
                        token = MSECCrypto.decrypt_data(encrypted_token, password)
                        if token is None:
                            return None
                    except Exception as e:
                        return None

                # 解析JSON数据
                roles = json.loads(roles_json) if roles_json else None
                checkin_history = json.loads(history_json) if history_json else None

                return {
                    'token': token,
                    'token_exp': token_exp,
                    'roles': roles,
                    'checkin_history': checkin_history,
                    'last_updated': last_updated
                }

        except Exception as e:
            print(f"获取用户数据失败: {e}")
            return None

    def is_token_valid(self, username: str, password: str) -> bool:
        """
        检查token是否有效（未过期）

        Args:
            username: 用户名
            password: 用户密码

        Returns:
            token是否有效
        """
        user_data = self.get_user_data(username, password)
        if not user_data or not user_data.get('token') or not user_data.get('token_exp'):
            return False

        # 检查是否过期（提前5分钟判断过期）
        current_time = int(time.time())
        token_exp = user_data['token_exp']

        return current_time < (token_exp - 300)  # 提前5分钟

    def debug_database_content(self):
        """调试：查看数据库中的所有内容"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM users')
                results = cursor.fetchall()

                print("数据库内容调试:")
                print("=" * 50)
                if results:
                    for row in results:
                        print(f"ID: {row[0]}")
                        print(f"Username: {row[1]}")
                        print(f"Encrypted Token: {'存在' if row[2] else '不存在'}")
                        print(f"Token Exp: {row[3]}")
                        print(f"Roles: {'存在' if row[4] else '不存在'}")
                        print(f"Checkin History: {'存在' if row[5] else '不存在'}")
                        print(f"Last Updated: {row[6]}")
                        print("-" * 30)
                else:
                    print("数据库为空")
                print("=" * 50)
        except Exception as e:
            print(f"调试数据库内容失败: {e}")


class MSECConfig:
    """MSEC配置类"""

    # 基础配置
    BASE_URL = "https://msec.nsfocus.com"
    LOGIN_URL = "https://msec.nsfocus.com/auth/login"

    # API端点
    API_ENDPOINTS = {
        'captcha': '/backend_api/account/captcha',
        'login': '/backend_api/account/login',
        'user_info': '/backend_api/account/info',
        'points': '/backend_api/point/common/get',
        'roles': '/backend_api/rbac/role/self/list',
        'checkin_history': '/backend_api/checkin/history',
        'checkin': '/backend_api/checkin/checkin'
    }

    # 请求头配置（仅包含必要的请求头）
    HEADERS = {
        'Host': 'msec.nsfocus.com',
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Origin': 'https://msec.nsfocus.com',
        'Referer': 'https://msec.nsfocus.com/auth/login',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }

    # 超时配置
    REQUEST_TIMEOUT = 30
    RETRY_COUNT = 3
    RETRY_DELAY = 2


class MSECLogger:
    """简化的日志管理类"""

    def __init__(self, verbose=False):
        self.verbose = verbose

    def info(self, message: str):
        if self.verbose:
            print(f"ℹ️ {message}")

    def error(self, message: str):
        print(f"❌ {message}")

    def warning(self, message: str):
        print(f"⚠️ {message}")

    def success(self, message: str):
        print(f"✅ {message}")

    def debug(self, message: str):
        if self.verbose:
            print(f"🔍 {message}")

    def step(self, message: str):
        print(f"📋 {message}")

    def progress(self, message: str):
        print(f"⏳ {message}")


class MSECClient:
    """MSEC客户端主类"""

    def __init__(self, username: str = None, password: str = None, master_password: str = None):
        self.username = username
        self.password = password
        self.master_password = master_password
        self.session = requests.Session()
        self.jwt_token = None
        self.user_info = None
        self.user_id = None
        self.logger = MSECLogger(verbose=False)  # 默认简洁模式
        self.db = MSECDatabase()

        # 设置基础请求头
        self.session.headers.update(MSECConfig.HEADERS)

        # 如果提供了用户名和主密码，尝试从数据库加载凭据
        if username and master_password and not password:
            self._load_credentials_from_db()

        # 尝试从数据库加载token
        if self.username and self.password:
            self._load_token_from_db()

    def _load_credentials_from_db(self):
        """从数据库加载用户凭据"""
        try:
            username, password = self.db.get_credentials(self.username, self.master_password)
            if username and password:
                self.username = username
                self.password = password
                return True
            else:
                return False
        except Exception as e:
            return False

    def save_credentials_to_db(self) -> bool:
        """保存用户凭据到数据库"""
        if not self.username or not self.password or not self.master_password:
            self.logger.error("无法保存凭据：用户名、密码或主密码未设置")
            return False

        return self.db.save_credentials(self.username, self.password, self.master_password)

    def _load_token_from_db(self):
        """从数据库加载token"""
        try:
            if self.db.is_token_valid(self.username, self.password):
                user_data = self.db.get_user_data(self.username, self.password)
                if user_data and user_data.get('token'):
                    self.jwt_token = user_data['token']
                    self.session.headers['Authorization'] = self.jwt_token
                    return True
        except Exception as e:
            self.logger.debug(f"从数据库加载token失败: {e}")
        return False

    def _save_token_to_db(self, token: str):
        """保存token到数据库"""
        if not self.user_id:
            self.logger.warning("无法保存token：用户ID未设置")
            return

        try:
            # 解析JWT token获取过期时间
            import jwt
            decoded = jwt.decode(token, options={"verify_signature": False})
            token_exp = decoded.get('exp', 0)

            success = self.db.save_user_data(
                user_id=self.user_id,
                username=self.username,
                password=self.password,
                token=token,
                token_exp=token_exp
            )
            if success:
                self.logger.info("Token保存到数据库成功")
            else:
                self.logger.error("Token保存到数据库失败")
        except Exception as e:
            # 如果无法解析JWT，使用默认过期时间（24小时后）
            token_exp = int(time.time()) + 24 * 3600
            success = self.db.save_user_data(
                user_id=self.user_id,
                username=self.username,
                password=self.password,
                token=token,
                token_exp=token_exp
            )
            if success:
                self.logger.warning(f"无法解析JWT过期时间，使用默认值: {e}")
            else:
                self.logger.error(f"Token保存到数据库失败: {e}")

    def _save_user_data_to_db(self, roles: Dict = None, checkin_history: Dict = None):
        """保存用户数据到数据库"""
        if not self.user_id:
            self.logger.warning("无法保存用户数据：用户ID未设置")
            return

        self.db.save_user_data(
            user_id=self.user_id,
            username=self.username,
            password=self.password,
            roles=roles,
            checkin_history=checkin_history
        )

    def is_logged_in(self) -> bool:
        """检查是否已登录（token有效）"""
        return self.jwt_token is not None and self.db.is_token_valid(self.username, self.password)

    def _force_update_all_user_data(self):
        """强制更新所有用户数据到数据库"""
        if not self.user_id or not self.jwt_token:
            self.logger.warning("无法强制更新用户数据：用户ID或token未设置")
            return

        self.logger.info("开始强制更新所有用户数据...")

        try:
            # 获取并保存用户信息（已在get_user_info中处理）
            user_info = self.get_user_info()

            # 获取并保存积分信息
            points_info = self.get_points()

            # 获取并保存角色信息
            roles_info = self.get_roles()

            # 获取并保存签到历史（本周数据）
            start_date, days = get_week_checkin_params()
            history_info = self.get_checkin_history(start_date, days)

            # 最后保存token（确保所有数据都已更新）
            self._save_token_to_db(self.jwt_token)

            self.logger.info("所有用户数据强制更新完成")

        except Exception as e:
            self.logger.error(f"强制更新用户数据失败: {e}")

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                     retry_count: int = MSECConfig.RETRY_COUNT) -> Optional[requests.Response]:
        """
        发送HTTP请求的通用方法

        Args:
            method: HTTP方法 (GET, POST等)
            endpoint: API端点
            data: 请求数据
            retry_count: 重试次数

        Returns:
            Response对象或None
        """
        url = MSECConfig.BASE_URL + endpoint

        for attempt in range(retry_count + 1):
            try:


                if method.upper() == 'POST':
                    response = self.session.post(
                        url,
                        json=data or {},
                        timeout=MSECConfig.REQUEST_TIMEOUT
                    )
                else:
                    response = self.session.get(
                        url,
                        timeout=MSECConfig.REQUEST_TIMEOUT
                    )

                response.raise_for_status()
                return response

            except requests.exceptions.RequestException as e:
                if attempt < retry_count:
                    time.sleep(MSECConfig.RETRY_DELAY)
                else:
                    return None

    def get_captcha(self) -> Tuple[Optional[str], Optional[str]]:
        """
        获取验证码

        Returns:
            (captcha_id, captcha_image_base64) 或 (None, None)
        """
        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['captcha'])
        if not response:
            return None, None

        try:
            # 解析响应数据
            response_data = response.json()

            # 检查响应状态
            if response_data.get('status') != 200:
                return None, None

            # 提取验证码信息
            data = response_data.get('data', {})
            captcha_id = data.get('id')
            captcha_image = data.get('captcha')

            if not captcha_id or not captcha_image:
                return None, None

            # 显示验证码
            print(f"\n🔍 验证码图片:")
            print(captcha_image)
            print(f"📋 验证码ID: {captcha_id}\n")

            return captcha_id, captcha_image

        except json.JSONDecodeError as e:
            self.logger.error(f"获取验证码失败：JSON解析错误 - {str(e)}")
            return None, None
        except Exception as e:
            self.logger.error(f"获取验证码失败：未知错误 - {str(e)}")
            return None, None

    def smart_captcha_login(self, max_attempts: int = 3) -> bool:
        """
        智能验证码登录，支持验证码错误重试

        Args:
            max_attempts: 最大尝试次数

        Returns:
            登录是否成功
        """
        for attempt in range(max_attempts):
            if attempt == 0:
                print("🔐 正在获取验证码...")
            else:
                print(f"🔄 第{attempt + 1}次尝试...")

            # 获取验证码
            captcha_id, captcha_image = self.get_captcha()
            if not captcha_id or not captcha_image:
                print("❌ 验证码获取失败")
                if attempt < max_attempts - 1:
                    print("🔄 正在重新获取验证码...")
                    continue
                else:
                    return False

            # 用户输入验证码
            try:
                if attempt == 0:
                    prompt = "🔤 请输入验证码: "
                else:
                    prompt = f"🔤 验证码错误，请重新输入 (第{attempt + 1}次): "

                captcha_answer = input(prompt).strip()

                if not captcha_answer:
                    print("⚠️ 验证码答案不能为空")
                    if attempt < max_attempts - 1:
                        continue
                    else:
                        return False

                # 尝试登录
                login_result = self.login(captcha_id, captcha_answer)

                if login_result:
                    print("✅ 登录成功！")
                    return True
                else:
                    # 登录失败，可能是验证码错误
                    if attempt < max_attempts - 1:
                        print(f"❌ 验证码错误，还有 {max_attempts - attempt - 1} 次机会")
                        continue
                    else:
                        print("❌ 验证码登录失败，已达到最大尝试次数")
                        return False

            except KeyboardInterrupt:
                self.logger.info("用户取消了验证码输入")
                return False
            except Exception as e:
                self.logger.error(f"验证码输入过程出错: {e}")
                if attempt < max_attempts - 1:
                    continue
                else:
                    return False

        return False

    def login(self, captcha_id: str, captcha_answer: str) -> bool:
        """
        用户登录

        Args:
            captcha_id: 验证码ID
            captcha_answer: 验证码答案

        Returns:
            登录是否成功
        """
        self.logger.info("正在尝试登录...")

        # 构建登录请求数据
        login_data = {
            "captcha_answer": captcha_answer,
            "captcha_id": captcha_id,
            "password": self.password,
            "username": self.username
        }

        self.logger.debug(f"登录请求数据: {login_data}")

        # 临时更新Referer为登录页面
        original_referer = self.session.headers.get('Referer')
        self.session.headers['Referer'] = 'https://msec.nsfocus.com/auth/login'

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['login'], login_data)

        # 恢复原始Referer
        if original_referer:
            self.session.headers['Referer'] = original_referer

        if not response:
            self.logger.error("登录失败：请求失败")
            return False

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"登录响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"登录失败：服务器返回状态 {response_data.get('status')}")
                return False

            # 提取JWT token
            data = response_data.get('data', {})
            jwt_token = data.get('token')

            if not jwt_token:
                self.logger.error("登录失败：未获取到JWT token")
                return False

            # 保存JWT token并更新请求头
            self.jwt_token = jwt_token
            self.session.headers['Authorization'] = jwt_token

            self.logger.info("登录成功！")
            self.logger.info(f"JWT Token: {jwt_token[:50]}...")  # 只显示前50个字符

            return True

        except json.JSONDecodeError as e:
            self.logger.error(f"登录失败：JSON解析错误 - {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"登录失败：未知错误 - {str(e)}")
            return False

    def get_user_info(self) -> Optional[Dict]:
        """
        获取用户信息

        Returns:
            用户信息字典或None
        """
        if not self.jwt_token:
            self.logger.error("获取用户信息失败：未登录")
            return None

        self.logger.info("正在获取用户信息...")

        # 更新Referer为主页
        self.session.headers['Referer'] = 'https://msec.nsfocus.com/'

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['user_info'])
        if not response:
            self.logger.error("获取用户信息失败：请求失败")
            return None

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"用户信息响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"获取用户信息失败：服务器返回状态 {response_data.get('status')}")
                return None

            # 提取用户信息
            data = response_data.get('data', {})
            user_info = data.get('user', {})

            if not user_info:
                self.logger.error("获取用户信息失败：响应数据不完整")
                return None

            # 保存用户信息和用户ID
            self.user_info = user_info
            self.user_id = user_info.get('id')

            if not self.user_id:
                self.logger.error("获取用户信息失败：未找到用户ID")
                return None

            print(f"👤 用户: {user_info.get('username')} (ID: {self.user_id})")

            # 如果有JWT token但还没有保存到数据库，现在保存
            if self.jwt_token and self.user_id:
                self._save_token_to_db(self.jwt_token)

            return user_info

        except json.JSONDecodeError as e:
            self.logger.error(f"获取用户信息失败：JSON解析错误 - {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"获取用户信息失败：未知错误 - {str(e)}")
            return None

    def get_points(self) -> Optional[Dict]:
        """
        获取用户积分信息

        Returns:
            积分信息字典或None
        """
        if not self.jwt_token:
            self.logger.error("获取积分信息失败：未登录")
            return None

        self.logger.info("正在获取积分信息...")

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['points'])
        if not response:
            self.logger.error("获取积分信息失败：请求失败")
            return None

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"积分信息响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"获取积分信息失败：服务器返回状态 {response_data.get('status')}")
                return None

            # 提取积分信息
            data = response_data.get('data', {})

            if 'accrued' not in data or 'total' not in data:
                self.logger.error("获取积分信息失败：响应数据不完整")
                return None

            print(f"💰 积分: {data.get('accrued')}/{data.get('total')}")

            return data

        except json.JSONDecodeError as e:
            self.logger.error(f"获取积分信息失败：JSON解析错误 - {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"获取积分信息失败：未知错误 - {str(e)}")
            return None

    def get_roles(self) -> Optional[Dict]:
        """
        获取用户角色信息

        Returns:
            角色信息字典或None
        """
        if not self.jwt_token:
            self.logger.error("获取角色信息失败：未登录")
            return None

        self.logger.info("正在获取角色信息...")

        # 构建请求数据
        role_data = {
            "limit": 100,
            "offset": 0
        }

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['roles'], role_data)
        if not response:
            self.logger.error("获取角色信息失败：请求失败")
            return None

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"角色信息响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"获取角色信息失败：服务器返回状态 {response_data.get('status')}")
                return None

            # 提取角色信息
            data = response_data.get('data', {})
            role_list = data.get('list', [])

            if not role_list:
                self.logger.warning("未获取到角色信息")
                return data

            role_names = []
            for role in role_list:
                role_name = role.get('role_name', '未知角色')

                # 处理Unicode编码的角色名称
                if isinstance(role_name, str) and '\\u' in role_name:
                    try:
                        # 解码Unicode转义序列
                        role_name = role_name.encode().decode('unicode_escape')
                    except Exception:
                        # 如果解码失败，保持原始值
                        pass

                expire_time = role.get('expire_time', 0)
                if expire_time == 0:
                    role_names.append(f"{role_name}(永久)")
                else:
                    role_names.append(f"{role_name}(限时)")

            if role_names:
                print(f"🎭 角色: {', '.join(role_names)}")
            else:
                print("🎭 角色: 无")

            # 保存角色信息到数据库
            self._save_user_data_to_db(roles=data)

            return data

        except json.JSONDecodeError as e:
            self.logger.error(f"获取角色信息失败：JSON解析错误 - {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"获取角色信息失败：未知错误 - {str(e)}")
            return None

    def get_checkin_history(self, start_date: str, days: int = 5) -> Optional[Dict]:
        """
        获取签到历史

        Args:
            start_date: 开始日期 (YYYY-MM-DD格式)
            days: 查询天数

        Returns:
            签到历史字典或None
        """
        if not self.jwt_token:
            self.logger.error("获取签到历史失败：未登录")
            return None

        self.logger.info(f"正在获取签到历史 (从 {start_date} 开始，{days} 天)...")

        # 构建请求数据
        history_data = {
            "start_date": start_date,
            "days": days
        }

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['checkin_history'], history_data)
        if not response:
            self.logger.error("获取签到历史失败：请求失败")
            return None

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"签到历史响应数据: {response_data}")

            # 检查响应状态
            if response_data.get('status') != 200:
                self.logger.error(f"获取签到历史失败：服务器返回状态 {response_data.get('status')}")
                return None

            # 提取签到历史信息
            data = response_data.get('data', {})
            records_list = data.get('records_list', {})
            checkin_records = records_list.get('list', [])
            total_records = records_list.get('total', 0)

            print(f"📅 本周签到记录: {total_records}次")

            # 保存签到历史到数据库
            self._save_user_data_to_db(checkin_history=data)

            return data

        except json.JSONDecodeError as e:
            self.logger.error(f"获取签到历史失败：JSON解析错误 - {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"获取签到历史失败：未知错误 - {str(e)}")
            return None

    def checkin(self) -> bool:
        """
        执行签到

        Returns:
            签到是否成功
        """
        if not self.jwt_token:
            self.logger.error("签到失败：未登录")
            return False

        self.logger.info("正在执行签到...")

        response = self._make_request('POST', MSECConfig.API_ENDPOINTS['checkin'])
        if not response:
            self.logger.error("签到失败：请求失败")
            return False

        try:
            # 解析响应数据
            response_data = response.json()
            self.logger.debug(f"签到响应数据: {response_data}")

            # 检查响应状态
            status = response_data.get('status')
            if status == 200:
                print("✅ 签到成功！")
                return True
            elif status == 400:
                print("⚠️ 今日已签到")
                return True  # 已签到也算成功
            else:
                print(f"❌ 签到失败 (状态: {status})")
                return False

        except json.JSONDecodeError as e:
            self.logger.error(f"签到失败：JSON解析错误 - {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"签到失败：未知错误 - {str(e)}")
            return False

    def auto_checkin_flow(self) -> bool:
        """
        自动签到完整流程

        Returns:
            整个流程是否成功
        """
        print("🚀 开始自动签到...")

        try:
            # 检查是否已有有效token
            if self.is_logged_in():
                print("✅ 检测到有效token，跳过登录步骤")
            else:
                # 步骤1-2: 智能验证码登录
                print("🔐 正在进行验证码登录...")
                login_success = self.smart_captcha_login(max_attempts=3)

                if not login_success:
                    print("❌ 登录失败")
                    return False

            # 获取用户信息和数据
            print("📊 正在获取用户数据...")
            user_info = self.get_user_info()
            points_info = self.get_points()
            roles_info = self.get_roles()

            # 获取签到历史
            start_date, days = get_week_checkin_params()
            history_before = self.get_checkin_history(start_date, days)

            # 执行签到
            print("📝 正在执行签到...")
            checkin_success = self.checkin()

            if not checkin_success:
                print("❌ 签到失败")
                return False

            # 确认签到结果
            print("🔍 正在确认签到结果...")
            time.sleep(2)  # 等待2秒确保数据更新
            history_after = self.get_checkin_history(start_date, days)

            if history_after:
                records_after = history_after.get('records_list', {}).get('list', [])
                records_before = history_before.get('records_list', {}).get('list', []) if history_before else []

                if len(records_after) > len(records_before):
                    print("✅ 签到确认成功")
                else:
                    print("⚠️ 签到状态未确定")

            print("🎉 自动签到完成！")
            return True

        except KeyboardInterrupt:
            print("\n❌ 用户中断了签到流程")
            return False
        except Exception as e:
            print(f"❌ 签到流程失败: {str(e)}")
            return False


def test_captcha():
    """测试验证码获取功能"""
    print("🔍 验证码获取测试")
    print("═" * 50)

    # 创建一个临时客户端用于测试
    client = MSECClient("test", "test")

    # 测试获取验证码
    captcha_id, captcha_image = client.get_captcha()

    if captcha_id and captcha_image:
        print("✅ 验证码获取成功")
        print(f"📋 ID: {captcha_id}")
        print(f"📏 图片大小: {len(captcha_image)} 字符")
    else:
        print("❌ 验证码获取失败")


def test_login():
    """测试登录功能"""
    print("🔐 登录功能测试")
    print("═" * 50)

    # 获取用户凭据
    username = input("👤 用户名: ").strip()
    password = input("🔑 密码: ").strip()

    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return

    # 创建客户端
    client = MSECClient(username, password)

    # 智能验证码登录
    print("\n🧪 开始登录测试")
    success = client.smart_captcha_login(max_attempts=3)

    if success:
        print("✅ 登录成功")
        print(f"🎫 Token: {client.jwt_token[:50]}...")
    else:
        print("❌ 登录失败")


def test_user_info():
    """测试用户信息获取功能"""
    print("MSEC用户信息获取测试")
    print("=" * 50)

    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()

    if not username or not password:
        print("错误：用户名和密码不能为空")
        return

    # 创建客户端
    client = MSECClient(username, password)

    # 智能验证码登录
    print("\n步骤1-2: 智能验证码登录")
    success = client.smart_captcha_login(max_attempts=3)

    if not success:
        print("❌ 验证码登录失败！")
        return

    print("✅ 登录成功！")

    # 获取用户信息
    print("\n步骤3: 获取用户信息")
    user_info = client.get_user_info()

    # 获取积分信息
    print("\n步骤4: 获取积分信息")
    points_info = client.get_points()

    # 获取角色信息
    print("\n步骤5: 获取角色信息")
    roles_info = client.get_roles()

    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"用户信息: {'✅ 成功' if user_info else '❌ 失败'}")
    print(f"积分信息: {'✅ 成功' if points_info else '❌ 失败'}")
    print(f"角色信息: {'✅ 成功' if roles_info else '❌ 失败'}")
    print("="*50)


def test_checkin():
    """测试签到功能"""
    print("MSEC签到功能测试")
    print("=" * 50)

    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()

    if not username or not password:
        print("错误：用户名和密码不能为空")
        return

    # 创建客户端
    client = MSECClient(username, password)

    # 智能验证码登录
    print("\n步骤1-2: 智能验证码登录")
    success = client.smart_captcha_login(max_attempts=3)

    if not success:
        print("❌ 验证码登录失败！")
        return

    print("✅ 登录成功！")

    # 获取签到历史
    print("\n步骤3: 获取签到历史")
    start_date, days = get_week_checkin_params()
    history_before = client.get_checkin_history(start_date, days)

    # 执行签到
    print("\n步骤4: 执行签到")
    checkin_success = client.checkin()

    # 再次获取签到历史
    print("\n步骤5: 确认签到结果")
    if checkin_success:
        time.sleep(2)  # 等待数据更新
        history_after = client.get_checkin_history(start_date, days)

    # 总结
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"签到历史获取: {'✅ 成功' if history_before else '❌ 失败'}")
    print(f"签到操作: {'✅ 成功' if checkin_success else '❌ 失败'}")
    print("="*50)


def test_database():
    """测试数据库功能"""
    print("MSEC数据库功能测试")
    print("=" * 50)

    # 获取用户凭据
    username = input("请输入用户名: ").strip()
    password = input("请输入密码: ").strip()

    if not username or not password:
        print("错误：用户名和密码不能为空")
        return

    # 创建客户端
    client = MSECClient(username, password)

    print("\n步骤1: 测试数据库初始化")
    print(f"数据库文件: {client.db.db_path}")

    print("\n步骤2: 测试token持久化")
    if client.is_logged_in():
        print("✅ 检测到有效token，无需重新登录")
        print(f"Token: {client.jwt_token[:50]}...")
    else:
        print("❌ 未检测到有效token，需要重新登录")

        # 智能验证码登录
        print("\n智能验证码登录...")
        success = client.smart_captcha_login(max_attempts=3)

        if success:
            print("✅ 登录成功，token已保存到数据库")
        else:
            print("❌ 登录失败！")
            return

    print("\n步骤3: 测试数据存储")
    # 获取并保存用户数据
    user_info = client.get_user_info()
    points_info = client.get_points()
    roles_info = client.get_roles()

    start_date, days = get_week_checkin_params()
    history_info = client.get_checkin_history(start_date, days)

    print("\n步骤4: 验证数据库存储")
    # 调试：查看数据库实际内容
    client.db.debug_database_content()

    # 直接检查数据库中的数据
    user_data = client.db.get_user_data(username, password)
    if user_data:
        print("✅ 用户数据读取成功")
        print(f"Token存在: {'是' if user_data.get('token') else '否'}")
        if user_data.get('token_exp'):
            print(f"Token过期时间: {datetime.fromtimestamp(user_data['token_exp']).strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"角色信息: {'已存储' if user_data.get('roles') else '未存储'}")
        print(f"签到历史: {'已存储' if user_data.get('checkin_history') else '未存储'}")

        # 检查token是否有效
        is_valid = client.db.is_token_valid(username, password)
        print(f"Token有效性: {'有效' if is_valid else '无效/过期'}")
    else:
        print("❌ 用户数据读取失败")

    # 重新创建客户端，测试数据加载
    print("\n创建新客户端实例测试...")
    client2 = MSECClient(username, password)

    if client2.is_logged_in():
        print("✅ 新客户端token加载成功")
    else:
        print("❌ 新客户端token加载失败")

    # 总结
    print("\n" + "="*50)
    print("数据库功能测试总结:")
    print(f"数据库初始化: ✅ 成功")
    print(f"Token持久化: {'✅ 成功' if client.is_logged_in() else '❌ 失败'}")
    print(f"数据存储: {'✅ 成功' if user_info and roles_info else '❌ 失败'}")
    print(f"数据加载: {'✅ 成功' if client2.is_logged_in() else '❌ 失败'}")
    print("="*50)


def test_captcha_retry():
    """测试验证码错误重试功能"""
    print("🔄 验证码重试测试")
    print("═" * 50)
    print("💡 可以故意输入错误验证码来测试重试功能")

    # 获取用户凭据
    username = input("\n👤 用户名: ").strip()
    password = input("🔑 密码: ").strip()

    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return

    # 创建客户端
    client = MSECClient(username, password)

    # 测试智能验证码登录
    print("\n🧪 开始测试 (最多3次尝试)")

    success = client.smart_captcha_login(max_attempts=3)

    if success:
        print("\n🎉 重试机制测试成功！")
    else:
        print("\n❌ 重试机制测试失败")





def add_new_user():
    """添加新用户"""
    print("\n➕ 添加新用户")
    print("─" * 30)

    username = input("👤 用户名: ").strip()
    password = input("🔑 密码: ").strip()
    master_password = input("🔐 主密码 (用于加密): ").strip()

    if not username or not password or not master_password:
        print("❌ 所有字段都必须填写")
        return

    # 创建客户端
    client = MSECClient(username, password, master_password)

    # 保存凭据到数据库
    if client.save_credentials_to_db():
        print("✅ 用户凭据保存成功")

        # 执行首次登录和数据同步
        print("\n🔄 正在执行首次登录...")
        success = client.auto_checkin_flow()

        if success:
            print("\n🎉 用户添加完成！")
            print("💡 下次使用: python msec_auto_checkin.py")
        else:
            print("\n⚠️ 用户已保存，但首次登录失败")
    else:
        print("❌ 用户保存失败")


def test_menu():
    """测试功能菜单"""
    print("\n🧪 测试功能")
    print("─" * 30)
    print("1. 🔍 验证码获取")
    print("2. 🔐 登录功能")
    print("3. 👤 用户信息")
    print("4. 📝 签到功能")
    print("5. 💾 数据库功能")
    print("6. 🔄 验证码重试")

    choice = input("\n🔢 选择功能 (1-6): ").strip()

    if choice == "1":
        test_captcha()
    elif choice == "2":
        test_login()
    elif choice == "3":
        test_user_info()
    elif choice == "4":
        test_checkin()
    elif choice == "5":
        test_database()
    elif choice == "6":
        test_captcha_retry()
    else:
        print("❌ 无效选择")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='MSEC自动签到脚本 - 智能登录和签到系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python msec_auto_checkin.py           # 默认执行自动签到
  python msec_auto_checkin.py useradd   # 添加新用户账号
  python msec_auto_checkin.py test      # 运行测试功能

注意:
  首次使用请先添加用户账号: python msec_auto_checkin.py useradd
        '''
    )

    parser.add_argument(
        'action',
        nargs='?',
        default='checkin',
        choices=['checkin', 'useradd', 'test'],
        help='执行的操作: checkin(默认,自动签到), useradd(添加用户), test(测试功能)'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='MSEC自动签到脚本 v2.0.0'
    )

    return parser.parse_args()


def auto_checkin_with_user_selection():
    """自动签到流程（带用户选择）"""
    db = MSECDatabase()
    saved_users = db.list_saved_users()

    if not saved_users:
        print("❌ 未检测到已保存的用户账号")
        print("请先添加用户账号: python msec_auto_checkin.py useradd")
        return False

    if len(saved_users) == 1:
        # 只有一个用户，直接使用
        selected_user = saved_users[0]
        print(f"👤 用户: {selected_user}")
    else:
        # 多个用户，让用户选择
        print("👥 选择用户:")
        for i, user in enumerate(saved_users, 1):
            print(f"  {i}. {user}")

        try:
            choice = input(f"\n🔢 请选择 (1-{len(saved_users)}): ").strip()
            choice_num = int(choice)
            if 1 <= choice_num <= len(saved_users):
                selected_user = saved_users[choice_num - 1]
            else:
                print("❌ 无效选择")
                return False
        except ValueError:
            print("❌ 请输入数字")
            return False

    # 获取主密码
    master_password = input(f"🔐 主密码: ").strip()

    if not master_password:
        print("❌ 主密码不能为空")
        return False

    # 创建客户端并执行自动签到
    client = MSECClient(username=selected_user, master_password=master_password)

    if not client.username or not client.password:
        print("❌ 主密码错误或用户数据损坏")
        return False

    print(f"✅ 用户 {client.username} 凭据加载成功")

    # 执行自动签到
    success = client.auto_checkin_flow()

    if success:
        print("\n🎉 自动签到完成！")
        return True
    else:
        print("\n❌ 自动签到失败！")
        return False


def main():
    """主函数"""
    try:
        args = parse_arguments()

        print("🚀 MSEC自动签到脚本 v2.0.0")
        print("═" * 50)

        if args.action == 'checkin':
            # 默认执行自动签到
            auto_checkin_with_user_selection()

        elif args.action == 'useradd':
            # 添加新用户
            add_new_user()

        elif args.action == 'test':
            # 运行测试功能
            test_menu()

    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()